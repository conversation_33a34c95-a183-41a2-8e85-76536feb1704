#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
痴呆症检测模型集成训练脚本
该脚本读取已处理的特征数据，为每组特征训练单独的模型，然后集成结果
"""

import os
import json
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import Model, load_model, save_model
from tensorflow.keras.layers import Input, Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from tensorflow.keras.utils import to_categorical
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, recall_score, confusion_matrix
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 设置随机种子以确保可重复性
np.random.seed(42)
tf.random.set_seed(42)

class DementiaEnsembleModel:
    def __init__(self, data_path=None, model_dir="ensemble_models"):
        """初始化集成模型类
        
        Args:
            data_path: 训练数据路径，如果为None则需要手动加载数据
            model_dir: 模型保存目录
        """
        self.data_path = data_path
        self.model_dir = model_dir
        self.group_models = {}
        self.group_performances = {}
        self.feature_groups = {}
        self.class_weights = None
        self.n_classes = None
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建模型保存目录
        os.makedirs(model_dir, exist_ok=True)
        
        # 如果提供了数据路径，则加载数据
        if data_path:
            self.load_data(data_path)
    
    def load_data(self, data_path):
        """从CSV文件加载数据
        
        Args:
            data_path: 数据文件路径
        """
        print(f"加载数据: {data_path}")
        self.data = pd.read_csv(data_path)
        print(f"数据形状: {self.data.shape}")
        
        # 检查数据是否包含标签列
        if 'label' not in self.data.columns:
            raise ValueError("数据必须包含'label'列作为目标变量")
        
        # 提取标签
        self.y = self.data['label'].values
        self.n_classes = len(np.unique(self.y))
        print(f"检测到 {self.n_classes} 个类别")
        
        # 计算类别权重
        class_counts = np.bincount(self.y)
        self.class_weights = {i: len(self.y) / (self.n_classes * count) for i, count in enumerate(class_counts)}
        print(f"类别权重: {self.class_weights}")
        
        # 自动识别特征组
        self._identify_feature_groups()
        
        # 划分训练集和验证集
        self._split_data()
    
    def _identify_feature_groups(self):
        """自动识别数据中的特征组"""
        # 排除标签列
        feature_cols = [col for col in self.data.columns if col != 'label']
        
        # 根据列名前缀识别特征组
        self.feature_groups = {
            'demographic': [col for col in feature_cols if col.startswith(('age', 'gender', 'edu', 'demographic'))],
            'acoustic': [col for col in feature_cols if col.startswith(('pitch', 'jitter', 'shimmer', 'hnr', 'acoustic'))],
            'mfcc': [col for col in feature_cols if col.startswith('mfcc')],
            'linguistic': [col for col in feature_cols if col.startswith(('word', 'pause', 'speech', 'linguistic'))]
        }
        
        # 将未分类的特征放入'other'组
        categorized = []
        for group in self.feature_groups.values():
            categorized.extend(group)
        
        other_features = [col for col in feature_cols if col not in categorized]
        if other_features:
            self.feature_groups['other'] = other_features
        
        # 移除空特征组
        self.feature_groups = {k: v for k, v in self.feature_groups.items() if v}
        
        # 打印特征组信息
        print("\n识别到的特征组:")
        for group, features in self.feature_groups.items():
            print(f"  {group}: {len(features)} 个特征")
            if len(features) <= 5:  # 如果特征较少，打印所有特征
                print(f"    {', '.join(features)}")
            else:  # 否则只打印前5个
                print(f"    {', '.join(features[:5])}... 等")
    
    def _split_data(self, test_size=0.2, random_state=42):
        """划分训练集和验证集"""
        # 划分数据
        indices = np.arange(len(self.y))
        train_indices, val_indices = train_test_split(
            indices, test_size=test_size, random_state=random_state, stratify=self.y
        )
        
        # 创建训练集和验证集
        self.train_data = self.data.iloc[train_indices]
        self.val_data = self.data.iloc[val_indices]
        
        self.y_train = self.y[train_indices]
        self.y_val = self.y[val_indices]
        
        # 转换为分类格式
        self.y_train_cat = to_categorical(self.y_train, self.n_classes)
        self.y_val_cat = to_categorical(self.y_val, self.n_classes)
        
        print(f"\n数据划分完成:")
        print(f"  训练集: {len(self.y_train)} 样本")
        print(f"  验证集: {len(self.y_val)} 样本")
        
        # 准备每个特征组的数据
        self.X_train_groups = {}
        self.X_val_groups = {}
        
        for group, features in self.feature_groups.items():
            # 标准化每组特征
            scaler = StandardScaler()
            self.X_train_groups[group] = scaler.fit_transform(self.train_data[features])
            self.X_val_groups[group] = scaler.transform(self.val_data[features])
    
    def train_group_models(self, epochs=50, batch_size=32, patience=15):
        """为每个特征组训练单独的模型"""
        print("\n开始训练特征组专用模型...")
        
        # 存储每个特征组的模型和预测
        self.group_models = {}
        self.group_predictions = {}
        self.group_performances = {}
        
        # 为每组特征创建和训练模型
        for group, features in self.feature_groups.items():
            print(f"\n训练 {group} 特征模型...")
            
            # 获取该组特征的数据
            X_train_group = self.X_train_groups[group]
            X_val_group = self.X_val_groups[group]
            
            # 构建该组特征的模型
            input_shape = X_train_group.shape[1]
            
            # 根据特征组类型定制模型架构
            inputs = Input(shape=(input_shape,))
            
            if group == 'demographic':
                # 人口统计学特征 - 简单模型
                x = Dense(16, activation='relu')(inputs)
                x = BatchNormalization()(x)
                x = Dense(8, activation='relu')(x)
                x = BatchNormalization()(x)
                
            elif group == 'mfcc':
                # MFCC特征 - 深层模型
                x = Dense(64, activation='relu')(inputs)
                x = BatchNormalization()(x)
                x = Dropout(0.3)(x)
                x = Dense(32, activation='relu')(x)
                x = BatchNormalization()(x)
                x = Dropout(0.2)(x)
                x = Dense(16, activation='relu')(x)
                x = BatchNormalization()(x)
                
            elif group == 'linguistic':
                # 语言特征 - 中等复杂度
                x = Dense(32, activation='relu')(inputs)
                x = BatchNormalization()(x)
                x = Dropout(0.3)(x)
                x = Dense(16, activation='relu')(x)
                x = BatchNormalization()(x)
                
            else:
                # 其他特征组 - 通用架构
                x = Dense(32, activation='relu')(inputs)
                x = BatchNormalization()(x)
                x = Dropout(0.2)(x)
                x = Dense(16, activation='relu')(x)
                x = BatchNormalization()(x)
            
            # 输出层
            outputs = Dense(self.n_classes, activation='softmax')(x)
            
            # 创建模型
            group_model = Model(inputs=inputs, outputs=outputs)
            
            # 编译模型
            group_model.compile(
                optimizer=Adam(learning_rate=0.001),
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            # 打印模型摘要
            print(f"{group} 模型摘要:")
            group_model.summary()
            
            # 训练模型
            callbacks = [
                EarlyStopping(monitor='val_loss', patience=patience, restore_best_weights=True)
            ]
            
            history = group_model.fit(
                X_train_group, self.y_train_cat,
                validation_data=(X_val_group, self.y_val_cat),
                epochs=epochs,
                batch_size=batch_size,
                class_weight=self.class_weights,
                callbacks=callbacks,
                verbose=1
            )
            
            # 评估模型
            y_pred_prob = group_model.predict(X_val_group)
            y_pred = np.argmax(y_pred_prob, axis=1)
            
            accuracy = accuracy_score(self.y_val, y_pred)
            recalls = recall_score(self.y_val, y_pred, average=None)
            
            print(f"{group} 模型性能:")
            print(f"  准确率: {accuracy:.4f}")
            for i, recall in enumerate(recalls):
                print(f"  类别 {i} 召回率: {recall:.4f}")
            
            # 存储模型和预测
            self.group_models[group] = group_model
            self.group_predictions[group] = y_pred_prob
            self.group_performances[group] = {
                'accuracy': accuracy,
                'recalls': recalls,
                'dementia_recall': recalls[0] if self.n_classes > 1 else recalls  # 假设类别0是痴呆症
            }
            
            # 保存模型
            model_path = os.path.join(self.model_dir, f"{group}_model_{self.timestamp}.h5")
            group_model.save(model_path)
            print(f"{group} 模型已保存到 {model_path}")
        
        # 打印每个特征组模型的性能
        print("\n各特征组模型性能比较:")
        for group, perf in self.group_performances.items():
            dementia_recall = perf['dementia_recall'][0] if isinstance(perf['dementia_recall'], np.ndarray) else perf['dementia_recall']
            print(f"{group}: 准确率={perf['accuracy']:.4f}, 痴呆症召回率={dementia_recall:.4f}")
        
        # 确定最佳特征组
        if self.n_classes > 1:
            best_group = max(self.group_performances.items(), 
                            key=lambda x: x[1]['dementia_recall'][0] if isinstance(x[1]['dementia_recall'], np.ndarray) 
                                        else x[1]['dementia_recall'])[0]
            best_recall = self.group_performances[best_group]['dementia_recall'][0] if isinstance(self.group_performances[best_group]['dementia_recall'], np.ndarray) else self.group_performances[best_group]['dementia_recall']
            print(f"\n痴呆症召回率最高的特征组: {best_group} (召回率={best_recall:.4f})")
    
    def create_ensemble(self):
        """创建集成模型"""
        print("\n开始集成各特征组模型...")
        
        if not self.group_predictions:
            raise ValueError("必须先训练特征组模型")
        
        # 方法1: 简单平均集成
        ensemble_pred_prob = np.zeros_like(next(iter(self.group_predictions.values())))
        for group, pred in self.group_predictions.items():
            ensemble_pred_prob += pred
        ensemble_pred_prob /= len(self.group_predictions)
        
        ensemble_pred = np.argmax(ensemble_pred_prob, axis=1)
        ensemble_accuracy = accuracy_score(self.y_val, ensemble_pred)
        ensemble_recalls = recall_score(self.y_val, ensemble_pred, average=None)
        
        print("简单平均集成结果:")
        print(f"  准确率: {ensemble_accuracy:.4f}")
        for i, recall in enumerate(ensemble_recalls):
            print(f"  类别 {i} 召回率: {recall:.4f}")
        
        # 方法2: 加权集成 (根据痴呆症召回率加权)
        weighted_ensemble_pred_prob = np.zeros_like(next(iter(self.group_predictions.values())))
        total_weight = 0
        
        for group, pred in self.group_predictions.items():
            # 使用痴呆症召回率作为权重
            if self.n_classes > 1:
                weight = self.group_performances[group]['recalls'][0]  # 假设类别0是痴呆症
            else:
                weight = self.group_performances[group]['accuracy']
            weighted_ensemble_pred_prob += pred * weight
            total_weight += weight
        
        weighted_ensemble_pred_prob /= total_weight
        weighted_ensemble_pred = np.argmax(weighted_ensemble_pred_prob, axis=1)
        weighted_ensemble_accuracy = accuracy_score(self.y_val, weighted_ensemble_pred)
        weighted_ensemble_recalls = recall_score(self.y_val, weighted_ensemble_pred, average=None)
        
        print("\n加权集成结果 (按痴呆症召回率加权):")
        print(f"  准确率: {weighted_ensemble_accuracy:.4f}")
        for i, recall in enumerate(weighted_ensemble_recalls):
            print(f"  类别 {i} 召回率: {recall:.4f}")
        
        # 方法3: 偏向痴呆症的集成 (提高痴呆症类别的概率)
        biased_ensemble_pred_prob = ensemble_pred_prob.copy()
        # 增加痴呆症类别(假设是类别0)的概率
        biased_ensemble_pred_prob[:, 0] *= 1.5  # 增加50%
        biased_ensemble_pred = np.argmax(biased_ensemble_pred_prob, axis=1)
        biased_ensemble_accuracy = accuracy_score(self.y_val, biased_ensemble_pred)
        biased_ensemble_recalls = recall_score(self.y_val, biased_ensemble_pred, average=None)
        
        print("\n偏向痴呆症的集成结果:")
        print(f"  准确率: {biased_ensemble_accuracy:.4f}")
        for i, recall in enumerate(biased_ensemble_recalls):
            print(f"  类别 {i} 召回率: {recall:.4f}")
        
        # 选择最佳集成方法
        best_ensemble_method = "加权集成" if weighted_ensemble_recalls[0] > max(ensemble_recalls[0], biased_ensemble_recalls[0]) else \
                              "偏向痴呆症集成" if biased_ensemble_recalls[0] > ensemble_recalls[0] else "简单平均集成"
        
        print(f"\n最佳集成方法: {best_ensemble_method}")
        
        # 保存集成结果
        self.ensemble_results = {
            "simple_average": {
                "accuracy": float(ensemble_accuracy),
                "recalls": [float(r) for r in ensemble_recalls],
                "dementia_recall": float(ensemble_recalls[0])
            },
            "weighted_average": {
                "accuracy": float(weighted_ensemble_accuracy),
                "recalls": [float(r) for r in weighted_ensemble_recalls],
                "dementia_recall": float(weighted_ensemble_recalls[0])
            },
            "biased_ensemble": {
                "accuracy": float(biased_ensemble_accuracy),
                "recalls": [float(r) for r in biased_ensemble_recalls],
                "dementia_recall": float(biased_ensemble_recalls[0])
            },
            "best_method": best_ensemble_method,
            "feature_group_performances": {
                group: {
                    "accuracy": float(perf["accuracy"]),
                    "dementia_recall": float(perf["recalls"][0]) if self.n_classes > 1 else float(perf["recalls"])
                } for group, perf in self.group_performances.items()
            }
        }
        
        # 保存集成结果
        results_path = os.path.join(self.model_dir, f"ensemble_results_{self.timestamp}.json")
        with open(results_path, "w") as f:
            json.dump(self.ensemble_results, f, indent=2)
        print(f"集成结果已保存到 {results_path}")
        
        # 返回最佳集成方法的结果
        if best_ensemble_method == "加权集成":
            return weighted_ensemble_pred_prob, weighted_ensemble_accuracy, weighted_ensemble_recalls
        elif best_ensemble_method == "偏向痴呆症集成":
            return biased_ensemble_pred_prob, biased_ensemble_accuracy, biased_ensemble_recalls
        else:
            return ensemble_pred_prob, ensemble_accuracy, ensemble_recalls
    
    def visualize_results(self):
        """可视化结果"""
        if not hasattr(self, 'ensemble_results'):
            raise ValueError("必须先创建集成模型")
        
        # 创建结果可视化目录
        vis_dir = os.path.join(self.model_dir, "visualizations")
        os.makedirs(vis_dir, exist_ok=True)
        
        # 1. 特征组性能比较
        plt.figure(figsize=(10, 6))
        groups = list(self.group_performances.keys())
        accuracies = [self.group_performances[g]['accuracy'] for g in groups]
        dementia_recalls = [self.group_performances[g]['recalls'][0] if self.n_classes > 1 else self.group_performances[g]['recalls'] for g in groups]
        
        x = np.arange(len(groups))
        width = 0.35
        
        fig, ax = plt.subplots(figsize=(12, 7))
        rects1 = ax.bar(x - width/2, accuracies, width, label='准确率')
        rects2 = ax.bar(x + width/2, dementia_recalls, width, label='痴呆症召回率')
        
        ax.set_ylabel('得分')
        ax.set_title('各特征组模型性能比较')
        ax.set_xticks(x)
        ax.set_xticklabels(groups)
        ax.legend()
        
        # 添加数值标签
        def autolabel(rects):
            for rect in rects:
                height = rect.get_height()
                ax.annotate(f'{height:.2f}',
                            xy=(rect.get_x() + rect.get_width() / 2, height),
                            xytext=(0, 3),
                            textcoords="offset points",
                            ha='center', va='bottom')
        
        autolabel(rects1)
        autolabel(rects2)
        
        fig.tight_layout()
        plt.savefig(os.path.join(vis_dir, f"feature_group_performance_{self.timestamp}.png"))
        
        # 2. 集成方法比较
        plt.figure(figsize=(10, 6))
        methods = ["简单平均", "加权平均", "偏向痴呆症"]
        accuracies = [
            self.ensemble_results["simple_average"]["accuracy"],
            self.ensemble_results["weighted_average"]["accuracy"],
            self.ensemble_results["biased_ensemble"]["accuracy"]
        ]
        dementia_recalls = [
            self.ensemble_results["simple_average"]["dementia_recall"],
            self.ensemble_results["weighted_average"]["dementia_recall"],
            self.ensemble_results["biased_ensemble"]["dementia_recall"]
        ]
        
        x = np.arange(len(methods))
        width = 0.35
        
        fig, ax = plt.subplots(figsize=(10, 7))
        rects1 = ax.bar(x - width/2, accuracies, width, label='准确率')
        rects2 = ax.bar(x + width/2, dementia_recalls, width, label='痴呆症召回率')
        
        ax.set_ylabel('得分')
        ax.set_title('各集成方法性能比较')
        ax.set_xticks(x)
        ax.set_xticklabels(methods)
        ax.legend()
        
        autolabel(rects1)
        autolabel(rects2)
        
        fig.tight_layout()
        plt.savefig(os.path.join(vis_dir, f"ensemble_methods_comparison_{self.timestamp}.png"))
        
        # 3. 混淆矩阵
        # 使用最佳集成方法的预测
        best_method = self.ensemble_results["best_method"]
        if best_method == "加权集成":
            method_key = "weighted_average"
        elif best_method == "偏向痴呆症集成":
            method_key = "biased_ensemble"
        else:
            method_key = "simple_average"
        
        # 重新计算最佳方法的预测
        if best_method == "加权集成":
            weighted_ensemble_pred_prob = np.zeros_like(next(iter(self.group_predictions.values())))
            total_weight = 0
            
            for group, pred in self.group_predictions.items():
                weight = self.group_performances[group]['recalls'][0] if self.n_classes > 1 else self.group_performances[group]['recalls']
                weighted_ensemble_pred_prob += pred * weight
                total_weight += weight
            
            weighted_ensemble_pred_prob /= total_weight
            best_pred = np.argmax(weighted_ensemble_pred_prob, axis=1)
            
        elif best_method == "偏向痴呆症集成":
            ensemble_pred_prob = np.zeros_like(next(iter(self.group_predictions.values())))
            for group, pred in self.group_predictions.items():
                ensemble_pred_prob += pred
            ensemble_pred_prob /= len(self.group_predictions)
            
            biased_ensemble_pred_prob = ensemble_pred_prob.copy()
            biased_ensemble_pred_prob[:, 0] *= 1.5
            best_pred = np.argmax(biased_ensemble_pred_prob, axis=1)
            
        else:  # 简单平均
            ensemble_pred_prob = np.zeros_like(next(iter(self.group_predictions.values())))
            for group, pred in self.group_predictions.items():
                ensemble_pred_prob += pred
            ensemble_pred_prob /= len(self.group_predictions)
            best_pred = np.argmax(ensemble_pred_prob, axis=1)
        
        # 计算混淆矩阵
        cm = confusion_matrix(self.y_val, best_pred)
        
        # 绘制混淆矩阵
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title(f'混淆矩阵 - {best_method}')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.savefig(os.path.join(vis_dir, f"confusion_matrix_{self.timestamp}.png"))
        
        print(f"\n可视化结果已保存到 {vis_dir}")
    
    def save_ensemble_model(self, model_name=None):
        """保存集成模型的所有组件"""
        if not self.group_models:
            raise ValueError("必须先训练特征组模型")
        
        if model_name is None:
            model_name = f"dementia_ensemble_{self.timestamp}"
        
        # 创建模型目录
        model_path = os.path.join(self.model_dir, model_name)
        os.makedirs(model_path, exist_ok=True)
        
        # 保存每个特征组模型
        for group, model in self.group_models.items():
            group_model_path = os.path.join(model_path, f"{group}_model.h5")
            model.save(group_model_path)
        
        # 保存特征组信息
        feature_info = {group: list(features) for group, features in self.feature_groups.items()}
        with open(os.path.join(model_path, "feature_groups.json"), "w") as f:
            json.dump(feature_info, f, indent=2)
        
        # 保存集成配置
        if hasattr(self, 'ensemble_results'):
            with open(os.path.join(model_path, "ensemble_config.json"), "w") as f:
                json.dump({
                    "best_method": self.ensemble_results["best_method"],
                    "class_weights": {str(k): float(v) for k, v in self.class_weights.items()},
                    "n_classes": self.n_classes,
                    "timestamp": self.timestamp
                }, f, indent=2)
        
        print(f"\n集成模型已保存到 {model_path}")
        return model_path
    
    @classmethod
    def load_ensemble_model(cls, model_path):
        """加载保存的集成模型
        
        Args:
            model_path: 模型目录路径
        
        Returns:
            加载的集成模型实例
        """
        # 创建新实例
        ensemble = cls(data_path=None, model_dir=os.path.dirname(model_path))
        
        # 加载特征组信息
        with open(os.path.join(model_path, "feature_groups.json"), "r") as f:
            ensemble.feature_groups = json.load(f)
        
        # 加载集成配置
        with open(os.path.join(model_path, "ensemble_config.json"), "r") as f:
            config = json.load(f)
            ensemble.n_classes = config["n_classes"]
            ensemble.class_weights = {int(k): v for k, v in config["class_weights"].items()}
            ensemble.timestamp = config.get("timestamp", datetime.now().strftime("%Y%m%d_%H%M%S"))
        
        # 加载每个特征组模型
        ensemble.group_models = {}
        for group in ensemble.feature_groups.keys():
            group_model_path = os.path.join(model_path, f"{group}_model.h5")
            if os.path.exists(group_model_path):
                ensemble.group_models[group] = load_model(group_model_path)
        
        print(f"集成模型已从 {model_path} 加载")
        return ensemble
    
    def predict(self, X, method="best"):
        """使用集成模型进行预测
        
        Args:
            X: 特征数据，可以是DataFrame或特征组字典
            method: 集成方法，可以是"best"、"simple"、"weighted"或"biased"
        
        Returns:
            预测的类别和概率
        """
        if not self.group_models:
            raise ValueError("必须先训练或加载特征组模型")
        
        # 准备特征组数据
        X_groups = {}
        if isinstance(X, pd.DataFrame):
            for group, features in self.feature_groups.items():
                if all(feat in X.columns for feat in features):
                    X_groups[group] = X[features].values
                else:
                    raise ValueError(f"输入数据缺少 {group} 组的某些特征")
        elif isinstance(X, dict):
            X_groups = X
        else:
            raise ValueError("X必须是DataFrame或特征组字典")
        
        # 获取每个特征组模型的预测
        group_predictions = {}
        for group, model in self.group_models.items():
            if group in X_groups:
                group_predictions[group] = model.predict(X_groups[group])
        
        # 根据指定方法集成预测
        if method == "simple" or (method == "best" and not hasattr(self, 'ensemble_results')):
            # 简单平均
            ensemble_pred_prob = np.zeros_like(next(iter(group_predictions.values())))
            for pred in group_predictions.values():
                ensemble_pred_prob += pred
            ensemble_pred_prob /= len(group_predictions)
            ensemble_pred = np.argmax(ensemble_pred_prob, axis=1)
            return ensemble_pred, ensemble_pred_prob
            
        elif method == "weighted" or (method == "best" and self.ensemble_results["best_method"] == "加